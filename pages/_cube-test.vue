<script setup lang="ts">
import Markdown from "~/components/Markdown.vue";

// 测试用的markdown内容，包含cube chart链接
const testMarkdown = ref(`
# Network Performance Analysis

Based on my investigation, I found the following performance metrics:

## Key Findings

- **Target entity**: *********
- **Location**: link ACC-SW → CORE-SW
- **Stable Network Latency**: The average network latency for traffic to and from ********* remained stable and low, consistently measuring between 0.07ms and 0.09ms.
- **No Packet Loss**: The packet drop rate for all traffic associated with ********* was 0% during the analysis period, indicating that all data was successfully transmitted over the network.

You can view the detailed performance charts here:

[Network Latency Chart](cube://${encodeURIComponent(JSON.stringify({
  "timezone": "Asia/Shanghai",
  "timeDimensions": [
    {
      "dimension": "fct_flows_1m.timestamp",
      "granularity": "hour"
    }
  ],
  "measures": ["fct_flows_1m.connect_cnt"]
}))})

## Analysis and Recommendations

Since the network performance metrics appear healthy, the access failure is likely not caused by a network issue. I recommend investigating the host ********* itself for potential problems.

Here are some suggested next steps:

- **Check Server and Application Health**: Verify that the ATM application and the server hosting it are running correctly. Check the application logs for any errors or exceptions that might have occurred around the time of the access failure.
- **System Resources**: Examine the CPU, memory, and disk I/O on the server to ensure there are no resource constraints that could be causing the application to become unresponsive.
`);
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-4xl mx-auto">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          Cube Chart Test
        </h1>
        <p class="text-gray-600">
          Testing cube chart rendering in markdown content
        </p>
      </div>

      <div class="bg-white rounded-lg shadow-sm border p-6">
        <Markdown :source="testMarkdown" />
      </div>
    </div>
  </div>
</template>
